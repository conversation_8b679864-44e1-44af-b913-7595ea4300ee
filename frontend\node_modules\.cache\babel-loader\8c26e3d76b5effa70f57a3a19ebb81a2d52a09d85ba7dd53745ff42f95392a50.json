{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Listing Web App\\\\frontend\\\\src\\\\Components\\\\JobCard.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst JobCard = ({\n  job,\n  onEdit,\n  onDelete\n}) => {\n  const formatDate = dateString => {\n    try {\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    } catch (error) {\n      return 'Date not available';\n    }\n  };\n  const handleDelete = () => {\n    if (window.confirm(`Are you sure you want to delete the job \"${job.title}\" at ${job.company}?`)) {\n      onDelete(job.id);\n    }\n  };\n  const renderTags = () => {\n    if (!job.tags) return null;\n    const tags = Array.isArray(job.tags) ? job.tags : job.tags.split(',');\n    return tags.filter(tag => tag.trim()).map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"tag\",\n      children: tag.trim()\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"job-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"job-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"job-title\",\n        children: job.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-company\",\n        children: job.company\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-location\",\n        children: [\"\\uD83D\\uDCCD \", job.location]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"job-meta\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"job-type\",\n        children: job.job_type || 'Full-time'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"job-date\",\n        children: [\"Posted: \", formatDate(job.posting_date)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), job.tags && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"job-tags\",\n      children: renderTags()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 9\n    }, this), job.description && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"job-description\",\n      children: job.description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"job-actions\",\n      children: [job.url && /*#__PURE__*/_jsxDEV(\"a\", {\n        href: job.url,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        className: \"btn btn-view\",\n        children: \"View Original\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-edit\",\n        onClick: () => onEdit(job),\n        children: \"Edit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-delete\",\n        onClick: handleDelete,\n        children: \"Delete\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_c = JobCard;\nexport default JobCard;\nvar _c;\n$RefreshReg$(_c, \"JobCard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "JobCard", "job", "onEdit", "onDelete", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "error", "handleDelete", "window", "confirm", "title", "company", "id", "renderTags", "tags", "Array", "isArray", "split", "filter", "tag", "trim", "map", "index", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "location", "job_type", "posting_date", "description", "url", "href", "target", "rel", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Job Listing Web App/frontend/src/Components/JobCard.js"], "sourcesContent": ["import React from 'react';\n\nconst JobCard = ({ job, onEdit, onDelete }) => {\n  const formatDate = (dateString) => {\n    try {\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    } catch (error) {\n      return 'Date not available';\n    }\n  };\n\n  const handleDelete = () => {\n    if (window.confirm(`Are you sure you want to delete the job \"${job.title}\" at ${job.company}?`)) {\n      onDelete(job.id);\n    }\n  };\n\n  const renderTags = () => {\n    if (!job.tags) return null;\n    \n    const tags = Array.isArray(job.tags) ? job.tags : job.tags.split(',');\n    return tags.filter(tag => tag.trim()).map((tag, index) => (\n      <span key={index} className=\"tag\">\n        {tag.trim()}\n      </span>\n    ));\n  };\n\n  return (\n    <div className=\"job-card\">\n      <div className=\"job-header\">\n        <h3 className=\"job-title\">{job.title}</h3>\n        <div className=\"job-company\">{job.company}</div>\n        <div className=\"job-location\">📍 {job.location}</div>\n      </div>\n\n      <div className=\"job-meta\">\n        <span className=\"job-type\">{job.job_type || 'Full-time'}</span>\n        <span className=\"job-date\">\n          Posted: {formatDate(job.posting_date)}\n        </span>\n      </div>\n\n      {job.tags && (\n        <div className=\"job-tags\">\n          {renderTags()}\n        </div>\n      )}\n\n      {job.description && (\n        <div className=\"job-description\">\n          {job.description}\n        </div>\n      )}\n\n      <div className=\"job-actions\">\n        {job.url && (\n          <a \n            href={job.url} \n            target=\"_blank\" \n            rel=\"noopener noreferrer\" \n            className=\"btn btn-view\"\n          >\n            View Original\n          </a>\n        )}\n        <button \n          className=\"btn btn-edit\" \n          onClick={() => onEdit(job)}\n        >\n          Edit\n        </button>\n        <button \n          className=\"btn btn-delete\" \n          onClick={handleDelete}\n        >\n          Delete\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default JobCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAGA,CAAC;EAAEC,GAAG;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAC7C,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI;MACF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,OAAO,oBAAoB;IAC7B;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,MAAM,CAACC,OAAO,CAAC,4CAA4Cd,GAAG,CAACe,KAAK,QAAQf,GAAG,CAACgB,OAAO,GAAG,CAAC,EAAE;MAC/Fd,QAAQ,CAACF,GAAG,CAACiB,EAAE,CAAC;IAClB;EACF,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAAClB,GAAG,CAACmB,IAAI,EAAE,OAAO,IAAI;IAE1B,MAAMA,IAAI,GAAGC,KAAK,CAACC,OAAO,CAACrB,GAAG,CAACmB,IAAI,CAAC,GAAGnB,GAAG,CAACmB,IAAI,GAAGnB,GAAG,CAACmB,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC;IACrE,OAAOH,IAAI,CAACI,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACF,GAAG,EAAEG,KAAK,kBACnD7B,OAAA;MAAkB8B,SAAS,EAAC,KAAK;MAAAC,QAAA,EAC9BL,GAAG,CAACC,IAAI,CAAC;IAAC,GADFE,KAAK;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEV,CACP,CAAC;EACJ,CAAC;EAED,oBACEnC,OAAA;IAAK8B,SAAS,EAAC,UAAU;IAAAC,QAAA,gBACvB/B,OAAA;MAAK8B,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB/B,OAAA;QAAI8B,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAE7B,GAAG,CAACe;MAAK;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1CnC,OAAA;QAAK8B,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAE7B,GAAG,CAACgB;MAAO;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChDnC,OAAA;QAAK8B,SAAS,EAAC,cAAc;QAAAC,QAAA,GAAC,eAAG,EAAC7B,GAAG,CAACkC,QAAQ;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAENnC,OAAA;MAAK8B,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvB/B,OAAA;QAAM8B,SAAS,EAAC,UAAU;QAAAC,QAAA,EAAE7B,GAAG,CAACmC,QAAQ,IAAI;MAAW;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/DnC,OAAA;QAAM8B,SAAS,EAAC,UAAU;QAAAC,QAAA,GAAC,UACjB,EAAC1B,UAAU,CAACH,GAAG,CAACoC,YAAY,CAAC;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAELjC,GAAG,CAACmB,IAAI,iBACPrB,OAAA;MAAK8B,SAAS,EAAC,UAAU;MAAAC,QAAA,EACtBX,UAAU,CAAC;IAAC;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAEAjC,GAAG,CAACqC,WAAW,iBACdvC,OAAA;MAAK8B,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7B7B,GAAG,CAACqC;IAAW;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CACN,eAEDnC,OAAA;MAAK8B,SAAS,EAAC,aAAa;MAAAC,QAAA,GACzB7B,GAAG,CAACsC,GAAG,iBACNxC,OAAA;QACEyC,IAAI,EAAEvC,GAAG,CAACsC,GAAI;QACdE,MAAM,EAAC,QAAQ;QACfC,GAAG,EAAC,qBAAqB;QACzBb,SAAS,EAAC,cAAc;QAAAC,QAAA,EACzB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CACJ,eACDnC,OAAA;QACE8B,SAAS,EAAC,cAAc;QACxBc,OAAO,EAAEA,CAAA,KAAMzC,MAAM,CAACD,GAAG,CAAE;QAAA6B,QAAA,EAC5B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnC,OAAA;QACE8B,SAAS,EAAC,gBAAgB;QAC1Bc,OAAO,EAAE9B,YAAa;QAAAiB,QAAA,EACvB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACU,EAAA,GApFI5C,OAAO;AAsFb,eAAeA,OAAO;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}