[{"D:\\Job Listing Web App\\frontend\\src\\index.js": "1", "D:\\Job Listing Web App\\frontend\\src\\App.js": "2", "D:\\Job Listing Web App\\frontend\\src\\api.js": "3", "D:\\Job Listing Web App\\frontend\\src\\Components,Pages\\JobCard.js": "4", "D:\\Job Listing Web App\\frontend\\src\\Components,Pages\\Add-Edit job.js": "5", "D:\\Job Listing Web App\\frontend\\src\\Components,Pages\\Filter-Sort job.js": "6", "D:\\Job Listing Web App\\frontend\\src\\Components,Pages\\Delete-job.js": "7"}, {"size": 252, "mtime": 1754386321280, "results": "8", "hashOfConfig": "9"}, {"size": 6519, "mtime": 1754387613696, "results": "10", "hashOfConfig": "9"}, {"size": 2831, "mtime": 1754386314392, "results": "11", "hashOfConfig": "9"}, {"size": 1923, "mtime": 1754389795890, "results": "12", "hashOfConfig": "9"}, {"size": 6404, "mtime": 1754386422296, "results": "13", "hashOfConfig": "9"}, {"size": 4864, "mtime": 1754386447764, "results": "14", "hashOfConfig": "9"}, {"size": 432, "mtime": 1754387521730, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ndhrug", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Job Listing Web App\\frontend\\src\\index.js", [], [], "D:\\Job Listing Web App\\frontend\\src\\App.js", ["37"], [], "D:\\Job Listing Web App\\frontend\\src\\api.js", [], [], "D:\\Job Listing Web App\\frontend\\src\\Components,Pages\\JobCard.js", [], [], "D:\\Job Listing Web App\\frontend\\src\\Components,Pages\\Add-Edit job.js", [], [], "D:\\Job Listing Web App\\frontend\\src\\Components,Pages\\Filter-Sort job.js", ["38", "39"], [], "D:\\Job Listing Web App\\frontend\\src\\Components,Pages\\Delete-job.js", [], [], {"ruleId": "40", "severity": 1, "message": "41", "line": 21, "column": 6, "nodeType": "42", "endLine": 21, "endColumn": 15, "suggestions": "43"}, {"ruleId": "44", "severity": 1, "message": "45", "line": 12, "column": 10, "nodeType": "46", "messageId": "47", "endLine": 12, "endColumn": 28}, {"ruleId": "44", "severity": 1, "message": "48", "line": 13, "column": 10, "nodeType": "46", "messageId": "47", "endLine": 13, "endColumn": 23}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchJobs'. Either include it or remove the dependency array.", "ArrayExpression", ["49"], "no-unused-vars", "'availableLocations' is assigned a value but never used.", "Identifier", "unusedVar", "'availableTags' is assigned a value but never used.", {"desc": "50", "fix": "51"}, "Update the dependencies array to be: [fetchJobs, filters]", {"range": "52", "text": "53"}, [773, 782], "[fetchJobs, filters]"]