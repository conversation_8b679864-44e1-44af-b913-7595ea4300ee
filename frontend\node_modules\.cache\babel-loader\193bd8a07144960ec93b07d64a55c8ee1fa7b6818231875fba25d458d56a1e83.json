{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Listing Web App\\\\frontend\\\\src\\\\Components,Pages\\\\Delete job.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DeleteJob = ({\n  job,\n  onDelete\n}) => {\n  const handleDelete = () => {\n    if (window.confirm(`Are you sure you want to delete the job \"${job.title}\" at ${job.company}?`)) {\n      onDelete(job.id);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    className: \"btn btn-delete\",\n    onClick: handleDelete,\n    title: `Delete ${job.title}`,\n    children: \"Delete\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = DeleteJob;\nexport default DeleteJob;\nvar _c;\n$RefreshReg$(_c, \"DeleteJob\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "DeleteJob", "job", "onDelete", "handleDelete", "window", "confirm", "title", "company", "id", "className", "onClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Job Listing Web App/frontend/src/Components,Pages/Delete job.js"], "sourcesContent": ["import React from 'react';\n\nconst DeleteJob = ({ job, onDelete }) => {\n  const handleDelete = () => {\n    if (window.confirm(`Are you sure you want to delete the job \"${job.title}\" at ${job.company}?`)) {\n      onDelete(job.id);\n    }\n  };\n\n  return (\n    <button \n      className=\"btn btn-delete\" \n      onClick={handleDelete}\n      title={`Delete ${job.title}`}\n    >\n      Delete\n    </button>\n  );\n};\n\nexport default DeleteJob;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAC;EAAEC,GAAG;EAAEC;AAAS,CAAC,KAAK;EACvC,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,MAAM,CAACC,OAAO,CAAC,4CAA4CJ,GAAG,CAACK,KAAK,QAAQL,GAAG,CAACM,OAAO,GAAG,CAAC,EAAE;MAC/FL,QAAQ,CAACD,GAAG,CAACO,EAAE,CAAC;IAClB;EACF,CAAC;EAED,oBACET,OAAA;IACEU,SAAS,EAAC,gBAAgB;IAC1BC,OAAO,EAAEP,YAAa;IACtBG,KAAK,EAAE,UAAUL,GAAG,CAACK,KAAK,EAAG;IAAAK,QAAA,EAC9B;EAED;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAQ,CAAC;AAEb,CAAC;AAACC,EAAA,GAhBIhB,SAAS;AAkBf,eAAeA,SAAS;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}