# Backend-specific Git Ignore

# Environment Variables (CRITICAL - Contains database credentials)
.env
.env.local
.env.production
.env.development

# Database files
*.db
*.sqlite
*.sqlite3
job_listing.db

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# Flask
instance/
.webassets-cache

# SQLAlchemy
*.db-journal

# Logs
*.log
logs/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db
