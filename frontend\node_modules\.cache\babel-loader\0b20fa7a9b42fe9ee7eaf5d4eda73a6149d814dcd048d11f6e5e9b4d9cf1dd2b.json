{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Listing Web App\\\\frontend\\\\src\\\\Components,Pages\\\\JobCard.js\";\nimport React from 'react';\nimport DeleteJob from './Delete-job';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst JobCard = ({\n  job,\n  onEdit,\n  onDelete\n}) => {\n  const formatDate = dateString => {\n    try {\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    } catch (error) {\n      return 'Date not available';\n    }\n  };\n  const renderTags = () => {\n    if (!job.tags) return null;\n    const tags = Array.isArray(job.tags) ? job.tags : job.tags.split(',');\n    return tags.filter(tag => tag.trim()).map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"tag\",\n      children: tag.trim()\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"job-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"job-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"job-title\",\n        children: job.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-company\",\n        children: job.company\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-location\",\n        children: [\"\\uD83D\\uDCCD \", job.location]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"job-meta\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"job-type\",\n        children: job.job_type || 'Full-time'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"job-date\",\n        children: [\"Posted: \", formatDate(job.posting_date)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), job.tags && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"job-tags\",\n      children: renderTags()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 9\n    }, this), job.description && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"job-description\",\n      children: job.description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"job-actions\",\n      children: [job.url && /*#__PURE__*/_jsxDEV(\"a\", {\n        href: job.url,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        className: \"btn btn-view\",\n        children: \"View Original\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-edit\",\n        onClick: () => onEdit(job),\n        children: \"Edit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DeleteJob, {\n        job: job,\n        onDelete: onDelete\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n};\n_c = JobCard;\nexport default JobCard;\nvar _c;\n$RefreshReg$(_c, \"JobCard\");", "map": {"version": 3, "names": ["React", "DeleteJob", "jsxDEV", "_jsxDEV", "JobCard", "job", "onEdit", "onDelete", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "error", "renderTags", "tags", "Array", "isArray", "split", "filter", "tag", "trim", "map", "index", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "company", "location", "job_type", "posting_date", "description", "url", "href", "target", "rel", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Job Listing Web App/frontend/src/Components,Pages/JobCard.js"], "sourcesContent": ["import React from 'react';\nimport DeleteJob from './Delete-job';\n\nconst JobCard = ({ job, onEdit, onDelete }) => {\n  const formatDate = (dateString) => {\n    try {\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    } catch (error) {\n      return 'Date not available';\n    }\n  };\n\n\n\n  const renderTags = () => {\n    if (!job.tags) return null;\n    \n    const tags = Array.isArray(job.tags) ? job.tags : job.tags.split(',');\n    return tags.filter(tag => tag.trim()).map((tag, index) => (\n      <span key={index} className=\"tag\">\n        {tag.trim()}\n      </span>\n    ));\n  };\n\n  return (\n    <div className=\"job-card\">\n      <div className=\"job-header\">\n        <h3 className=\"job-title\">{job.title}</h3>\n        <div className=\"job-company\">{job.company}</div>\n        <div className=\"job-location\">📍 {job.location}</div>\n      </div>\n\n      <div className=\"job-meta\">\n        <span className=\"job-type\">{job.job_type || 'Full-time'}</span>\n        <span className=\"job-date\">\n          Posted: {formatDate(job.posting_date)}\n        </span>\n      </div>\n\n      {job.tags && (\n        <div className=\"job-tags\">\n          {renderTags()}\n        </div>\n      )}\n\n      {job.description && (\n        <div className=\"job-description\">\n          {job.description}\n        </div>\n      )}\n\n      <div className=\"job-actions\">\n        {job.url && (\n          <a \n            href={job.url} \n            target=\"_blank\" \n            rel=\"noopener noreferrer\" \n            className=\"btn btn-view\"\n          >\n            View Original\n          </a>\n        )}\n        <button\n          className=\"btn btn-edit\"\n          onClick={() => onEdit(job)}\n        >\n          Edit\n        </button>\n        <DeleteJob job={job} onDelete={onDelete} />\n      </div>\n    </div>\n  );\n};\n\nexport default JobCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,OAAO,GAAGA,CAAC;EAAEC,GAAG;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAC7C,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI;MACF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,OAAO,oBAAoB;IAC7B;EACF,CAAC;EAID,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACZ,GAAG,CAACa,IAAI,EAAE,OAAO,IAAI;IAE1B,MAAMA,IAAI,GAAGC,KAAK,CAACC,OAAO,CAACf,GAAG,CAACa,IAAI,CAAC,GAAGb,GAAG,CAACa,IAAI,GAAGb,GAAG,CAACa,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC;IACrE,OAAOH,IAAI,CAACI,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACF,GAAG,EAAEG,KAAK,kBACnDvB,OAAA;MAAkBwB,SAAS,EAAC,KAAK;MAAAC,QAAA,EAC9BL,GAAG,CAACC,IAAI,CAAC;IAAC,GADFE,KAAK;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEV,CACP,CAAC;EACJ,CAAC;EAED,oBACE7B,OAAA;IAAKwB,SAAS,EAAC,UAAU;IAAAC,QAAA,gBACvBzB,OAAA;MAAKwB,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBzB,OAAA;QAAIwB,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAEvB,GAAG,CAAC4B;MAAK;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1C7B,OAAA;QAAKwB,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAEvB,GAAG,CAAC6B;MAAO;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChD7B,OAAA;QAAKwB,SAAS,EAAC,cAAc;QAAAC,QAAA,GAAC,eAAG,EAACvB,GAAG,CAAC8B,QAAQ;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAEN7B,OAAA;MAAKwB,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBzB,OAAA;QAAMwB,SAAS,EAAC,UAAU;QAAAC,QAAA,EAAEvB,GAAG,CAAC+B,QAAQ,IAAI;MAAW;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/D7B,OAAA;QAAMwB,SAAS,EAAC,UAAU;QAAAC,QAAA,GAAC,UACjB,EAACpB,UAAU,CAACH,GAAG,CAACgC,YAAY,CAAC;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAEL3B,GAAG,CAACa,IAAI,iBACPf,OAAA;MAAKwB,SAAS,EAAC,UAAU;MAAAC,QAAA,EACtBX,UAAU,CAAC;IAAC;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAEA3B,GAAG,CAACiC,WAAW,iBACdnC,OAAA;MAAKwB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7BvB,GAAG,CAACiC;IAAW;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CACN,eAED7B,OAAA;MAAKwB,SAAS,EAAC,aAAa;MAAAC,QAAA,GACzBvB,GAAG,CAACkC,GAAG,iBACNpC,OAAA;QACEqC,IAAI,EAAEnC,GAAG,CAACkC,GAAI;QACdE,MAAM,EAAC,QAAQ;QACfC,GAAG,EAAC,qBAAqB;QACzBf,SAAS,EAAC,cAAc;QAAAC,QAAA,EACzB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CACJ,eACD7B,OAAA;QACEwB,SAAS,EAAC,cAAc;QACxBgB,OAAO,EAAEA,CAAA,KAAMrC,MAAM,CAACD,GAAG,CAAE;QAAAuB,QAAA,EAC5B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7B,OAAA,CAACF,SAAS;QAACI,GAAG,EAAEA,GAAI;QAACE,QAAQ,EAAEA;MAAS;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACY,EAAA,GA3EIxC,OAAO;AA6Eb,eAAeA,OAAO;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}