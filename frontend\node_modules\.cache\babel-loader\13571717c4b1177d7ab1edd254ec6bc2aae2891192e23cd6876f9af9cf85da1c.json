{"ast": null, "code": "import axios from 'axios';\n\n// Create axios instance with base configuration\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor\napi.interceptors.request.use(config => {\n  console.log(`Making ${config.method.toUpperCase()} request to ${config.url}`);\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  var _error$response;\n  console.error('API Error:', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message);\n  return Promise.reject(error);\n});\n\n// Job API functions\nexport const jobAPI = {\n  // Get all jobs with optional filters\n  getJobs: async (filters = {}) => {\n    try {\n      const params = new URLSearchParams();\n      Object.keys(filters).forEach(key => {\n        if (filters[key] && filters[key] !== '') {\n          params.append(key, filters[key]);\n        }\n      });\n      const response = await api.get(`/jobs?${params.toString()}`);\n      return response.data;\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      throw new Error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to fetch jobs');\n    }\n  },\n  // Get single job by ID\n  getJob: async id => {\n    try {\n      const response = await api.get(`/jobs/${id}`);\n      return response.data;\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      throw new Error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Failed to fetch job');\n    }\n  },\n  // Create new job\n  createJob: async jobData => {\n    try {\n      const response = await api.post('/jobs', jobData);\n      return response.data;\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      throw new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Failed to create job');\n    }\n  },\n  // Update existing job\n  updateJob: async (id, jobData) => {\n    try {\n      const response = await api.put(`/jobs/${id}`, jobData);\n      return response.data;\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      throw new Error(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || 'Failed to update job');\n    }\n  },\n  // Delete job\n  deleteJob: async id => {\n    try {\n      const response = await api.delete(`/jobs/${id}`);\n      return response.data;\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      throw new Error(((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.message) || 'Failed to delete job');\n    }\n  },\n  // Get job statistics\n  getJobStats: async () => {\n    try {\n      const response = await api.get('/jobs/stats');\n      return response.data;\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      throw new Error(((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.message) || 'Failed to fetch job statistics');\n    }\n  }\n};\n\n// Health check\nexport const healthCheck = async () => {\n  try {\n    const response = await api.get('/health');\n    return response.data;\n  } catch (error) {\n    throw new Error('API health check failed');\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "timeout", "headers", "interceptors", "request", "use", "config", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "_error$response", "data", "message", "jobAPI", "getJobs", "filters", "params", "URLSearchParams", "Object", "keys", "for<PERSON>ach", "key", "append", "get", "toString", "_error$response2", "_error$response2$data", "Error", "get<PERSON>ob", "id", "_error$response3", "_error$response3$data", "createJob", "jobData", "post", "_error$response4", "_error$response4$data", "updateJob", "put", "_error$response5", "_error$response5$data", "deleteJob", "delete", "_error$response6", "_error$response6$data", "getJobStats", "_error$response7", "_error$response7$data", "healthCheck"], "sources": ["D:/Job Listing Web App/frontend/src/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// Create axios instance with base configuration\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor\napi.interceptors.request.use(\n  (config) => {\n    console.log(`Making ${config.method.toUpperCase()} request to ${config.url}`);\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    console.error('API Error:', error.response?.data || error.message);\n    return Promise.reject(error);\n  }\n);\n\n// Job API functions\nexport const jobAPI = {\n  // Get all jobs with optional filters\n  getJobs: async (filters = {}) => {\n    try {\n      const params = new URLSearchParams();\n      \n      Object.keys(filters).forEach(key => {\n        if (filters[key] && filters[key] !== '') {\n          params.append(key, filters[key]);\n        }\n      });\n      \n      const response = await api.get(`/jobs?${params.toString()}`);\n      return response.data;\n    } catch (error) {\n      throw new Error(error.response?.data?.message || 'Failed to fetch jobs');\n    }\n  },\n\n  // Get single job by ID\n  getJob: async (id) => {\n    try {\n      const response = await api.get(`/jobs/${id}`);\n      return response.data;\n    } catch (error) {\n      throw new Error(error.response?.data?.message || 'Failed to fetch job');\n    }\n  },\n\n  // Create new job\n  createJob: async (jobData) => {\n    try {\n      const response = await api.post('/jobs', jobData);\n      return response.data;\n    } catch (error) {\n      throw new Error(error.response?.data?.message || 'Failed to create job');\n    }\n  },\n\n  // Update existing job\n  updateJob: async (id, jobData) => {\n    try {\n      const response = await api.put(`/jobs/${id}`, jobData);\n      return response.data;\n    } catch (error) {\n      throw new Error(error.response?.data?.message || 'Failed to update job');\n    }\n  },\n\n  // Delete job\n  deleteJob: async (id) => {\n    try {\n      const response = await api.delete(`/jobs/${id}`);\n      return response.data;\n    } catch (error) {\n      throw new Error(error.response?.data?.message || 'Failed to delete job');\n    }\n  },\n\n  // Get job statistics\n  getJobStats: async () => {\n    try {\n      const response = await api.get('/jobs/stats');\n      return response.data;\n    } catch (error) {\n      throw new Error(error.response?.data?.message || 'Failed to fetch job statistics');\n    }\n  }\n};\n\n// Health check\nexport const healthCheck = async () => {\n  try {\n    const response = await api.get('/health');\n    return response.data;\n  } catch (error) {\n    throw new Error('API health check failed');\n  }\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;EACrEC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACVC,OAAO,CAACC,GAAG,CAAC,UAAUF,MAAM,CAACG,MAAM,CAACC,WAAW,CAAC,CAAC,eAAeJ,MAAM,CAACK,GAAG,EAAE,CAAC;EAC7E,OAAOL,MAAM;AACf,CAAC,EACAM,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAjB,GAAG,CAACQ,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC1BU,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACTT,OAAO,CAACK,KAAK,CAAC,YAAY,EAAE,EAAAI,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,IAAI,KAAIL,KAAK,CAACM,OAAO,CAAC;EAClE,OAAOL,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMO,MAAM,GAAG;EACpB;EACAC,OAAO,EAAE,MAAAA,CAAOC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC/B,IAAI;MACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MAEpCC,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CAACK,OAAO,CAACC,GAAG,IAAI;QAClC,IAAIN,OAAO,CAACM,GAAG,CAAC,IAAIN,OAAO,CAACM,GAAG,CAAC,KAAK,EAAE,EAAE;UACvCL,MAAM,CAACM,MAAM,CAACD,GAAG,EAAEN,OAAO,CAACM,GAAG,CAAC,CAAC;QAClC;MACF,CAAC,CAAC;MAEF,MAAMZ,QAAQ,GAAG,MAAMpB,GAAG,CAACkC,GAAG,CAAC,SAASP,MAAM,CAACQ,QAAQ,CAAC,CAAC,EAAE,CAAC;MAC5D,OAAOf,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MAAA,IAAAmB,gBAAA,EAAAC,qBAAA;MACd,MAAM,IAAIC,KAAK,CAAC,EAAAF,gBAAA,GAAAnB,KAAK,CAACG,QAAQ,cAAAgB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBd,IAAI,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBd,OAAO,KAAI,sBAAsB,CAAC;IAC1E;EACF,CAAC;EAED;EACAgB,MAAM,EAAE,MAAOC,EAAE,IAAK;IACpB,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMpB,GAAG,CAACkC,GAAG,CAAC,SAASM,EAAE,EAAE,CAAC;MAC7C,OAAOpB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MAAA,IAAAwB,gBAAA,EAAAC,qBAAA;MACd,MAAM,IAAIJ,KAAK,CAAC,EAAAG,gBAAA,GAAAxB,KAAK,CAACG,QAAQ,cAAAqB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnB,IAAI,cAAAoB,qBAAA,uBAApBA,qBAAA,CAAsBnB,OAAO,KAAI,qBAAqB,CAAC;IACzE;EACF,CAAC;EAED;EACAoB,SAAS,EAAE,MAAOC,OAAO,IAAK;IAC5B,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAMpB,GAAG,CAAC6C,IAAI,CAAC,OAAO,EAAED,OAAO,CAAC;MACjD,OAAOxB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MAAA,IAAA6B,gBAAA,EAAAC,qBAAA;MACd,MAAM,IAAIT,KAAK,CAAC,EAAAQ,gBAAA,GAAA7B,KAAK,CAACG,QAAQ,cAAA0B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxB,IAAI,cAAAyB,qBAAA,uBAApBA,qBAAA,CAAsBxB,OAAO,KAAI,sBAAsB,CAAC;IAC1E;EACF,CAAC;EAED;EACAyB,SAAS,EAAE,MAAAA,CAAOR,EAAE,EAAEI,OAAO,KAAK;IAChC,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAMpB,GAAG,CAACiD,GAAG,CAAC,SAAST,EAAE,EAAE,EAAEI,OAAO,CAAC;MACtD,OAAOxB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MAAA,IAAAiC,gBAAA,EAAAC,qBAAA;MACd,MAAM,IAAIb,KAAK,CAAC,EAAAY,gBAAA,GAAAjC,KAAK,CAACG,QAAQ,cAAA8B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5B,IAAI,cAAA6B,qBAAA,uBAApBA,qBAAA,CAAsB5B,OAAO,KAAI,sBAAsB,CAAC;IAC1E;EACF,CAAC;EAED;EACA6B,SAAS,EAAE,MAAOZ,EAAE,IAAK;IACvB,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMpB,GAAG,CAACqD,MAAM,CAAC,SAASb,EAAE,EAAE,CAAC;MAChD,OAAOpB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MAAA,IAAAqC,gBAAA,EAAAC,qBAAA;MACd,MAAM,IAAIjB,KAAK,CAAC,EAAAgB,gBAAA,GAAArC,KAAK,CAACG,QAAQ,cAAAkC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsBhC,OAAO,KAAI,sBAAsB,CAAC;IAC1E;EACF,CAAC;EAED;EACAiC,WAAW,EAAE,MAAAA,CAAA,KAAY;IACvB,IAAI;MACF,MAAMpC,QAAQ,GAAG,MAAMpB,GAAG,CAACkC,GAAG,CAAC,aAAa,CAAC;MAC7C,OAAOd,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MAAA,IAAAwC,gBAAA,EAAAC,qBAAA;MACd,MAAM,IAAIpB,KAAK,CAAC,EAAAmB,gBAAA,GAAAxC,KAAK,CAACG,QAAQ,cAAAqC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnC,IAAI,cAAAoC,qBAAA,uBAApBA,qBAAA,CAAsBnC,OAAO,KAAI,gCAAgC,CAAC;IACpF;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMoC,WAAW,GAAG,MAAAA,CAAA,KAAY;EACrC,IAAI;IACF,MAAMvC,QAAQ,GAAG,MAAMpB,GAAG,CAACkC,GAAG,CAAC,SAAS,CAAC;IACzC,OAAOd,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;IACd,MAAM,IAAIqB,KAAK,CAAC,yBAAyB,CAAC;EAC5C;AACF,CAAC;AAED,eAAetC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}