'use strict';
require('../../modules/es.array.iterator');
require('../../modules/es.object.to-string');
require('../../modules/es.string.iterator');
require('../../modules/es.iterator.constructor');
require('../../modules/es.iterator.drop');
require('../../modules/es.iterator.every');
require('../../modules/es.iterator.filter');
require('../../modules/es.iterator.find');
require('../../modules/es.iterator.flat-map');
require('../../modules/es.iterator.for-each');
require('../../modules/es.iterator.map');
require('../../modules/es.iterator.reduce');
require('../../modules/es.iterator.some');
require('../../modules/es.iterator.take');
require('../../modules/es.iterator.to-array');
require('../../modules/esnext.iterator.chunks');
// TODO: drop from core-js@4
require('../../modules/esnext.iterator.constructor');
require('../../modules/esnext.iterator.range');
require('../../modules/esnext.iterator.sliding');
require('../../modules/esnext.iterator.windows');
require('../../modules/web.dom-collections.iterator');
var path = require('../../internals/path');

module.exports = path.Iterator.range;
